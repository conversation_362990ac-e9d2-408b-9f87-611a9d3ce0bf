import {
  reactExtension,
  BlockStack,
  InlineStack,
  Text,
  Image,
  Link,
  useApi,
  useOrderConfirmation,
  useCustomer
} from '@shopify/ui-extensions-react/checkout';
import { useState, useEffect } from 'react';

export default reactExtension(
  'purchase.thank-you.block.render',
  () => <PromoCodeBanner />
);

function PromoCodeBanner() {
  const { query } = useApi();
  const orderConfirmation = useOrderConfirmation();
  const customer = useCustomer();
  const [promoBanner, setPromoBanner] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchPromoBanner() {
      try {
        // Prepare parameters similar to your original script
        const params = {
          _shopId: '1167',
          _bannerId: '3075',
          _customerFirstName: customer?.firstName || '',
          _customerLastName: customer?.lastName || '',
          _customerEmail: orderConfirmation?.order?.email || '',
          _customerPhone: orderConfirmation?.order?.phone || '',
          _customerGender: 'CUSTOMER_GENDER',
          _orderId: orderConfirmation?.order?.name || '',
          _orderValue: orderConfirmation?.order?.totalPrice?.amount || '',
          _orderCurrency: orderConfirmation?.order?.totalPrice?.currencyCode || '',
          _usedPromoCode: 'ORDER_PROMO_CODE', // You'd need to get this from order data
          method: 'main'
        };

        // Make API call to your backend/proxy to fetch promo banner
        // Note: You cannot directly call swopstore.com due to CORS restrictions
        const response = await fetch('/api/promo-banner', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(params)
        });

        if (response.ok) {
          const bannerData = await response.json();
          setPromoBanner(bannerData);
        }
      } catch (error) {
        console.error('Failed to fetch promo banner:', error);
      } finally {
        setLoading(false);
      }
    }

    if (orderConfirmation?.order) {
      fetchPromoBanner();
    }
  }, [orderConfirmation, customer]);

  if (loading) {
    return (
      <BlockStack spacing="base">
        <Text>Loading special offer...</Text>
      </BlockStack>
    );
  }

  if (!promoBanner) {
    return null;
  }

  return (
    <BlockStack spacing="base">
      {promoBanner.imageUrl && (
        <Link to={promoBanner.redirectUrl || '#'}>
          <Image
            source={promoBanner.imageUrl}
            alt={promoBanner.altText || 'Special Offer'}
          />
        </Link>
      )}
      {promoBanner.text && (
        <Text>{promoBanner.text}</Text>
      )}
    </BlockStack>
  );
}
