// Backend API endpoint (Node.js/Express example)
// This would be part of your Shopify app backend

app.post('/api/promo-banner', async (req, res) => {
  try {
    const {
      _shopId,
      _bannerId,
      _customerFirstName,
      _customerLastName,
      _customerEmail,
      _customerPhone,
      _customerGender,
      _orderId,
      _orderValue,
      _orderCurrency,
      _usedPromoCode
    } = req.body;

    // Build query parameters for swopstore.com
    const params = new URLSearchParams({
      _shopId,
      _bannerId,
      _customerFirstName,
      _customerLastName,
      _customerEmail,
      _customerPhone,
      _customerGender,
      _orderId,
      _orderValue,
      _orderCurrency,
      _usedPromoCode,
      method: 'main',
      jsc: 'iPromoCpnObj'
    });

    // Make request to swopstore.com from your backend
    const response = await fetch(`https://swopstore.com/wrapper.php?${params}`, {
      method: 'GET',
      headers: {
        'User-Agent': 'Shopify-App-Backend'
      }
    });

    if (response.ok) {
      const data = await response.text();
      
      // Parse the response and extract relevant banner information
      // This depends on what swopstore.com returns
      const bannerData = parseBannerResponse(data);
      
      res.json(bannerData);
    } else {
      res.status(500).json({ error: 'Failed to fetch banner' });
    }
  } catch (error) {
    console.error('Promo banner error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

function parseBannerResponse(responseText) {
  // Parse the JavaScript response from swopstore.com
  // Extract image URLs, text, links, etc.
  // Return structured data for the frontend
  
  return {
    imageUrl: 'extracted-image-url',
    redirectUrl: 'extracted-redirect-url',
    text: 'extracted-promo-text',
    altText: 'Special Offer'
  };
}
