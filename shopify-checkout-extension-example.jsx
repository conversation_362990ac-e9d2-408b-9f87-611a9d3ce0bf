import {
  reactExtension,
  Image,
  Link,
  BlockStack,
  useApi
} from '@shopify/ui-extensions-react/checkout';

export default reactExtension(
  'purchase.thank-you.block.render', // Thank you page target
  () => <BumpCelebrationBanner />
);

function BumpCelebrationBanner() {
  const { analytics } = useApi();
  
  const handleBannerClick = () => {
    // Track the community thank you event
    analytics.publish('custom_event', {
      event: 'community_thank_you',
      campaign: 'bump',
      source: 'thankyoupage'
    });
  };

  return (
    <BlockStack spacing="base">
      <Link
        to="https://superbottoms.com/pages/bump-celebration?utm_source=website&utm_medium=thankyoupage&utm_campaign=bump"
        onPress={handleBannerClick}
      >
        <Image
          source="https://cdn.shopify.com/s/files/1/0402/7370/7171/files/Thank-You-Page_11zon.webp?v=1743676954"
          alt="Bump Celebration Banner"
          fit="cover"
        />
      </Link>
    </BlockStack>
  );
}
